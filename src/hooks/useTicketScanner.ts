// Custom hook for ticket scanner functionality

import { useState, useCallback, useRef, useEffect } from 'react'
import { toast } from '@/components/ui/use-toast'
import { provideFeedback, initializeFeedback } from '@/utilities/scannerFeedback'
import type {
  ScannerState,
  ValidatedTicket,
  ScannerError,
  ScannerStats,
  ScannerSettings,
  ScanHistoryItem,
  ScanResult,
  QRCodeData,
  TicketValidationResponse,
  CheckinResponse
} from '@/types/TicketScanning'

const DEFAULT_SETTINGS: ScannerSettings = {
  autoRescanAfterCheckin: true,
  soundEnabled: true,
  vibrationEnabled: true,
  showScanHistory: false,
  scanTimeout: 30000
}

export const useTicketScanner = () => {
  // Core state
  const [state, setState] = useState<ScannerState>('idle')
  const [ticket, setTicket] = useState<ValidatedTicket | null>(null)
  const [error, setError] = useState<ScannerError | null>(null)
  const [scannerKey, setScannerKey] = useState(0)

  // Scanner statistics
  const [stats, setStats] = useState<ScannerStats>({
    totalScans: 0,
    successfulScans: 0,
    failedScans: 0,
    checkedInTickets: 0,
    sessionStartTime: new Date()
  })

  // Settings and history
  const [settings, setSettings] = useState<ScannerSettings>(DEFAULT_SETTINGS)
  const [history, setHistory] = useState<ScanHistoryItem[]>([])

  // Refs for cleanup and debouncing
  const scanTimeoutRef = useRef<NodeJS.Timeout>()
  const lastScanRef = useRef<string>('')
  const scanCooldownRef = useRef<NodeJS.Timeout>()

  // Initialize feedback on mount
  useEffect(() => {
    initializeFeedback()
  }, [])

  // API functions
  const validateTicketWithAPI = async (ticketData: QRCodeData): Promise<ValidatedTicket> => {
    const res = await fetch('/api/v1/checkin/scan', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(ticketData),
    })
    
    const data: TicketValidationResponse = await res.json()
    
    if (!res.ok || !data.success) {
      throw new Error(data.message || 'Ticket validation failed')
    }
    
    if (!data.ticket) {
      throw new Error('Invalid response from server')
    }
    
    return data.ticket
  }

  const checkinTicketWithAPI = async (ticket: ValidatedTicket): Promise<void> => {
    const res = await fetch('/api/v1/checkin', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ticketCode: ticket.ticketCode,
        eventId: ticket.eventId,
        eventScheduleId: ticket.eventScheduleId,
      }),
    })
    
    const data: CheckinResponse = await res.json()
    
    if (!res.ok || !data.success) {
      throw new Error(data.message || 'Check-in failed')
    }
  }

  // Add to scan history
  const addToHistory = useCallback((item: Omit<ScanHistoryItem, 'id' | 'timestamp'>) => {
    const historyItem: ScanHistoryItem = {
      ...item,
      id: Date.now().toString(),
      timestamp: new Date()
    }
    
    setHistory(prev => [historyItem, ...prev.slice(0, 49)]) // Keep last 50 items
  }, [])

  // Update statistics
  const updateStats = useCallback((type: 'scan' | 'success' | 'error' | 'checkin') => {
    setStats(prev => {
      const updated = { ...prev, lastScanTime: new Date() }
      
      switch (type) {
        case 'scan':
          updated.totalScans++
          break
        case 'success':
          updated.successfulScans++
          break
        case 'error':
          updated.failedScans++
          break
        case 'checkin':
          updated.checkedInTickets++
          break
      }
      
      return updated
    })
  }, [])

  // Handle QR code scan
  const handleScan = useCallback(async (results: ScanResult[]) => {
    if (!results?.[0]?.rawValue || state === 'validating' || state === 'checking-in') {
      return
    }

    const rawValue = results[0].rawValue

    // Prevent duplicate scans within cooldown period
    if (lastScanRef.current === rawValue && scanCooldownRef.current) {
      return
    }

    // Clear any existing timeout
    if (scanTimeoutRef.current) {
      clearTimeout(scanTimeoutRef.current)
    }

    // Set cooldown to prevent rapid duplicate scans
    lastScanRef.current = rawValue
    scanCooldownRef.current = setTimeout(() => {
      lastScanRef.current = ''
      scanCooldownRef.current = undefined
    }, 1000)

    setState('validating')
    setError(null)
    setTicket(null)
    updateStats('scan')

    // Provide immediate feedback
    if (settings.soundEnabled || settings.vibrationEnabled) {
      provideFeedback('scan', {
        sound: settings.soundEnabled,
        vibration: settings.vibrationEnabled
      })
    }

    try {
      let ticketData: QRCodeData
      
      try {
        ticketData = JSON.parse(rawValue)
      } catch {
        throw new Error('QR code is not a valid ticket format')
      }

      // Validate required fields
      if (!ticketData.ticketCode) {
        throw new Error('Invalid ticket: missing ticket code')
      }

      const validatedTicket = await validateTicketWithAPI(ticketData)
      
      setTicket(validatedTicket)
      setState('validated')
      updateStats('success')

      // Add to history
      addToHistory({
        ticketCode: validatedTicket.ticketCode,
        result: 'success',
        attendeeName: validatedTicket.attendeeName,
        seat: validatedTicket.seat
      })

      // Success feedback
      if (settings.soundEnabled || settings.vibrationEnabled) {
        provideFeedback('success', {
          sound: settings.soundEnabled,
          vibration: settings.vibrationEnabled
        })
      }

      toast({
        title: 'Vé hợp lệ!',
        description: `Mã vé: ${validatedTicket.ticketCode} - ${validatedTicket.attendeeName}`,
        duration: 3000,
        variant: 'success',
      })

    } catch (err: any) {
      const scannerError: ScannerError = {
        message: err.message || 'Unknown error occurred',
        type: err.name === 'TypeError' ? 'network' : 'validation'
      }

      setError(scannerError)
      setState('error')
      updateStats('error')

      // Add to history
      addToHistory({
        ticketCode: 'Unknown',
        result: 'error',
        message: scannerError.message
      })

      // Error feedback
      if (settings.soundEnabled || settings.vibrationEnabled) {
        provideFeedback('error', {
          sound: settings.soundEnabled,
          vibration: settings.vibrationEnabled
        })
      }

      toast({
        title: 'Không thành công',
        description: scannerError.message,
        variant: 'destructive',
      })

      // Auto-clear error after timeout
      setTimeout(() => {
        if (state === 'error') {
          setState('idle')
          setError(null)
        }
      }, 3000)
    }
  }, [state, settings, updateStats, addToHistory])

  // Handle check-in
  const handleCheckin = useCallback(async () => {
    if (!ticket || state !== 'validated') return

    setState('checking-in')

    try {
      await checkinTicketWithAPI(ticket)
      
      setState('checked-in')
      updateStats('checkin')

      // Update ticket state
      setTicket(prev => prev ? { ...prev, isCheckedIn: true, checkedIn: true } : null)

      // Add to history
      addToHistory({
        ticketCode: ticket.ticketCode,
        result: 'checked-in',
        attendeeName: ticket.attendeeName,
        seat: ticket.seat
      })

      // Check-in success feedback
      if (settings.soundEnabled || settings.vibrationEnabled) {
        provideFeedback('checkin', {
          sound: settings.soundEnabled,
          vibration: settings.vibrationEnabled
        })
      }

      toast({
        title: 'Check-in thành công!',
        description: `Vé ${ticket.ticketCode} đã được xác nhận check-in.`,
        duration: 3000,
        variant: 'success',
      })

      // Auto-rescan if enabled
      if (settings.autoRescanAfterCheckin) {
        setTimeout(() => {
          handleRescan()
        }, 2000)
      }

    } catch (err: any) {
      setState('validated') // Return to validated state
      
      const errorMessage = err.message || 'Check-in failed'
      
      // Error feedback
      if (settings.soundEnabled || settings.vibrationEnabled) {
        provideFeedback('error', {
          sound: settings.soundEnabled,
          vibration: settings.vibrationEnabled
        })
      }

      toast({
        title: 'Lỗi check-in',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [ticket, state, settings, updateStats, addToHistory])

  // Handle rescan
  const handleRescan = useCallback(() => {
    setState('idle')
    setTicket(null)
    setError(null)
    setScannerKey(prev => prev + 1) // Force scanner remount
    
    // Clear any pending timeouts
    if (scanTimeoutRef.current) {
      clearTimeout(scanTimeoutRef.current)
    }
    if (scanCooldownRef.current) {
      clearTimeout(scanCooldownRef.current)
    }
    
    lastScanRef.current = ''
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
    if (state === 'error') {
      setState('idle')
    }
  }, [state])

  // Update settings
  const updateSettings = useCallback((newSettings: Partial<ScannerSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }))
  }, [])

  // Clear history
  const clearHistory = useCallback(() => {
    setHistory([])
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (scanTimeoutRef.current) {
        clearTimeout(scanTimeoutRef.current)
      }
      if (scanCooldownRef.current) {
        clearTimeout(scanCooldownRef.current)
      }
    }
  }, [])

  return {
    // State
    state,
    ticket,
    error,
    stats,
    settings,
    history,
    scannerKey,
    
    // Actions
    handleScan,
    handleCheckin,
    handleRescan,
    clearError,
    updateSettings,
    clearHistory,
    
    // Computed properties
    isScanning: state === 'scanning' || state === 'idle',
    isValidating: state === 'validating',
    isCheckingIn: state === 'checking-in',
    canCheckin: state === 'validated' && ticket && !ticket.isCheckedIn && ticket.status === 'booked',
    canRescan: state !== 'validating' && state !== 'checking-in'
  }
}
