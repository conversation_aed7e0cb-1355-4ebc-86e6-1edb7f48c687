'use client'

import React, { useEffect, useState } from 'react'
import { <PERSON>anner } from '@yudiel/react-qr-scanner'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useTicketScanner } from '@/hooks/useTicketScanner'
import { ScannerOverlay } from '@/components/TicketScanner/ScannerOverlay'
import { TicketDisplay } from '@/components/TicketScanner/TicketDisplay'
import { ScannerControls } from '@/components/TicketScanner/ScannerControls'
import { cn } from '@/utilities/ui'
import { QrCode, Keyboard, X } from 'lucide-react'
import type { ScanResult } from '@/types/TicketScanning'

export default function CheckinScanPage() {
  const {
    state,
    ticket,
    error,
    stats,
    settings,
    history,
    scannerKey,
    handleScan,
    handleCheckin,
    handleRescan,
    // clearError, // Not used in this component
    updateSettings,
    clearHistory,
    isValidating,
    isCheckingIn,
    canCheckin,
    canRescan
  } = useTicketScanner()

  // Modal states
  const [showManualInput, setShowManualInput] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [_showSettings, setShowSettings] = useState(false)
  const [_showStats, setShowStats] = useState(false)
  const [manualTicketCode, setManualTicketCode] = useState('')

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement) return // Don't trigger when typing in inputs

      switch (event.key.toLowerCase()) {
        case 'r':
          if (canRescan) handleRescan()
          break
        case 'm':
          setShowManualInput(true)
          break
        case ' ':
          event.preventDefault()
          if (canCheckin) handleCheckin()
          break
        case 'h':
          setShowHistory(true)
          break
        case 's':
          setShowSettings(true)
          break
        case 'escape':
          setShowManualInput(false)
          setShowHistory(false)
          setShowSettings(false)
          setShowStats(false)
          break
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [canRescan, canCheckin, handleRescan, handleCheckin])

  // Handle manual ticket input
  const handleManualSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!manualTicketCode.trim()) return

    const mockScanResult: ScanResult[] = [{
      rawValue: JSON.stringify({ ticketCode: manualTicketCode.trim().toUpperCase() })
    }]

    await handleScan(mockScanResult)
    setManualTicketCode('')
    setShowManualInput(false)
  }

  // Scanner wrapper with error handling
  const handleScannerScan = (results: any[]) => {
    const scanResults: ScanResult[] = results.map(result => ({
      rawValue: result.rawValue,
      format: result.format,
      timestamp: Date.now()
    }))
    handleScan(scanResults)
  }

  const handleScannerError = (err: any) => {
    console.error('Scanner error:', err)
    // The error handling is now managed by the useTicketScanner hook
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-white">
      <div className="container mx-auto px-4 py-6 max-w-lg">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="text-3xl font-bold text-blue-900 mb-2">
            Quét vé check-in
          </h1>
          <p className="text-blue-600">
            Hệ thống quét vé tự động với phản hồi nhanh
          </p>
        </div>

        {/* Scanner Card */}
        <Card className="shadow-2xl border-0 bg-white/95 backdrop-blur-sm mb-6">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-center text-blue-900 flex items-center justify-center gap-2">
              <QrCode className="w-6 h-6" />
              Camera Scanner
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              {/* Scanner Container */}
              <div className={cn(
                "w-full aspect-square rounded-xl overflow-hidden border-2 shadow-lg transition-all duration-300",
                state === 'validated' ? 'border-green-400' :
                state === 'error' ? 'border-red-400' :
                state === 'validating' ? 'border-blue-400' :
                'border-gray-300'
              )}>
                <Scanner
                  key={scannerKey}
                  onScan={handleScannerScan}
                  onError={handleScannerError}
                  constraints={{ facingMode: 'environment' }}
                  formats={['qr_code']}
                  styles={{
                    container: { width: '100%', height: '100%' },
                    video: { width: '100%', height: '100%', objectFit: 'cover' },
                  }}
                />

                {/* Scanner Overlay */}
                <ScannerOverlay
                  state={state}
                  message={error?.message}
                />
              </div>

              {/* Status Message */}
              {state === 'idle' && !ticket && (
                <div className="mt-4 text-center">
                  <p className="text-gray-600 text-sm">
                    Đưa mã QR vào khung camera để quét vé
                  </p>
                  <p className="text-gray-400 text-xs mt-1">
                    Đảm bảo ánh sáng đủ và giữ camera ổn định
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        {/* Ticket Display */}
        {ticket && (
          <TicketDisplay
            ticket={ticket}
            onCheckin={handleCheckin}
            onRescan={handleRescan}
            isCheckingIn={isCheckingIn}
          />
        )}

        {/* Scanner Controls */}
        <ScannerControls
          onRescan={handleRescan}
          onToggleSound={() => updateSettings({ soundEnabled: !settings.soundEnabled })}
          onToggleVibration={() => updateSettings({ vibrationEnabled: !settings.vibrationEnabled })}
          onShowHistory={() => setShowHistory(true)}
          onShowStats={() => setShowStats(true)}
          onShowSettings={() => setShowSettings(true)}
          onManualInput={() => setShowManualInput(true)}
          stats={stats}
          settings={settings}
          disabled={isValidating || isCheckingIn}
        />

        {/* Manual Input Modal */}
        <Dialog open={showManualInput} onOpenChange={setShowManualInput}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Keyboard className="w-5 h-5" />
                Nhập mã vé thủ công
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleManualSubmit} className="space-y-4">
              <div>
                <Label htmlFor="ticketCode">Mã vé</Label>
                <Input
                  id="ticketCode"
                  value={manualTicketCode}
                  onChange={(e) => setManualTicketCode(e.target.value)}
                  placeholder="Nhập mã vé (VD: TK123456)"
                  className="mt-1"
                  autoFocus
                />
              </div>
              <div className="flex gap-2">
                <Button type="submit" disabled={!manualTicketCode.trim()} className="flex-1">
                  Kiểm tra vé
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowManualInput(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* History Modal */}
        <Dialog open={showHistory} onOpenChange={setShowHistory}>
          <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Lịch sử quét vé</DialogTitle>
            </DialogHeader>
            <div className="space-y-2">
              {history.length === 0 ? (
                <p className="text-gray-500 text-center py-8">Chưa có lịch sử quét vé</p>
              ) : (
                history.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{item.ticketCode}</p>
                      {item.attendeeName && (
                        <p className="text-sm text-gray-600">{item.attendeeName} - {item.seat}</p>
                      )}
                      <p className="text-xs text-gray-500">
                        {item.timestamp.toLocaleTimeString('vi-VN')}
                      </p>
                    </div>
                    <div className={cn(
                      "px-2 py-1 rounded text-xs font-medium",
                      item.result === 'success' ? 'bg-green-100 text-green-800' :
                      item.result === 'checked-in' ? 'bg-blue-100 text-blue-800' :
                      'bg-red-100 text-red-800'
                    )}>
                      {item.result === 'success' ? 'Hợp lệ' :
                       item.result === 'checked-in' ? 'Đã check-in' :
                       'Lỗi'}
                    </div>
                  </div>
                ))
              )}
            </div>
            {history.length > 0 && (
              <div className="flex justify-end pt-4 border-t">
                <Button variant="outline" onClick={clearHistory}>
                  Xóa lịch sử
                </Button>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
