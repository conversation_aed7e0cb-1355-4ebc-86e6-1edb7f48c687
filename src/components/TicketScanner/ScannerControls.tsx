// Scanner controls component with quick actions

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  RotateCcw, 
  Volume2, 
  VolumeX, 
  Vibrate, 
  History, 
  BarChart3,
  Settings,
  Keyboard,
  Zap
} from 'lucide-react'
import { cn } from '@/utilities/ui'
import type { ScannerStats, ScannerSettings } from '@/types/TicketScanning'

interface ScannerControlsProps {
  onRescan: () => void
  onToggleSound: () => void
  onToggleVibration: () => void
  onShowHistory: () => void
  onShowStats: () => void
  onShowSettings: () => void
  onManualInput: () => void
  stats: ScannerStats
  settings: ScannerSettings
  disabled: boolean
  className?: string
}

export const ScannerControls: React.FC<ScannerControlsProps> = ({
  onRescan,
  onToggleSound,
  onToggleVibration,
  onShowHistory,
  onShowStats,
  onShowSettings,
  onManualInput,
  stats,
  settings,
  disabled,
  className
}) => {
  const [showQuickStats, setShowQuickStats] = useState(false)

  const getSuccessRate = () => {
    if (stats.totalScans === 0) return 0
    return Math.round((stats.successfulScans / stats.totalScans) * 100)
  }

  const getSessionDuration = () => {
    const now = new Date()
    const duration = now.getTime() - stats.sessionStartTime.getTime()
    const minutes = Math.floor(duration / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    return `${minutes}m`
  }

  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Quick Stats Bar */}
      <div className="flex items-center justify-between p-3 bg-white rounded-lg shadow-sm border">
        <div className="flex items-center gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{stats.totalScans}</p>
            <p className="text-xs text-gray-500">Tổng quét</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{stats.checkedInTickets}</p>
            <p className="text-xs text-gray-500">Check-in</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">{getSuccessRate()}%</p>
            <p className="text-xs text-gray-500">Thành công</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            <Zap className="w-3 h-3 mr-1" />
            {getSessionDuration()}
          </Badge>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowQuickStats(!showQuickStats)}
            className="h-8 w-8 p-0"
          >
            <BarChart3 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Expanded Stats */}
      {showQuickStats && (
        <div className="grid grid-cols-2 gap-3 p-3 bg-gray-50 rounded-lg border">
          <div className="text-center p-2 bg-white rounded">
            <p className="text-lg font-semibold text-red-600">{stats.failedScans}</p>
            <p className="text-xs text-gray-500">Lỗi</p>
          </div>
          <div className="text-center p-2 bg-white rounded">
            <p className="text-lg font-semibold text-gray-600">
              {stats.lastScanTime ? 
                new Date(stats.lastScanTime).toLocaleTimeString('vi-VN', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                }) : 
                '--:--'
              }
            </p>
            <p className="text-xs text-gray-500">Quét cuối</p>
          </div>
        </div>
      )}

      {/* Primary Controls */}
      <div className="grid grid-cols-2 gap-3">
        <Button
          variant="outline"
          onClick={onRescan}
          disabled={disabled}
          className="flex items-center justify-center gap-2 h-12 border-blue-400 text-blue-700 hover:bg-blue-50"
        >
          <RotateCcw className="w-5 h-5" />
          Quét lại
        </Button>
        
        <Button
          variant="outline"
          onClick={onManualInput}
          disabled={disabled}
          className="flex items-center justify-center gap-2 h-12 border-purple-400 text-purple-700 hover:bg-purple-50"
        >
          <Keyboard className="w-5 h-5" />
          Nhập tay
        </Button>
      </div>

      {/* Secondary Controls */}
      <div className="grid grid-cols-4 gap-2">
        <Button
          variant={settings.soundEnabled ? "default" : "outline"}
          size="sm"
          onClick={onToggleSound}
          disabled={disabled}
          className={cn(
            "flex flex-col items-center gap-1 h-16 p-2",
            settings.soundEnabled 
              ? "bg-green-600 hover:bg-green-700 text-white" 
              : "border-gray-300 text-gray-600 hover:bg-gray-50"
          )}
        >
          {settings.soundEnabled ? (
            <Volume2 className="w-5 h-5" />
          ) : (
            <VolumeX className="w-5 h-5" />
          )}
          <span className="text-xs">Âm thanh</span>
        </Button>

        <Button
          variant={settings.vibrationEnabled ? "default" : "outline"}
          size="sm"
          onClick={onToggleVibration}
          disabled={disabled}
          className={cn(
            "flex flex-col items-center gap-1 h-16 p-2",
            settings.vibrationEnabled 
              ? "bg-green-600 hover:bg-green-700 text-white" 
              : "border-gray-300 text-gray-600 hover:bg-gray-50"
          )}
        >
          <Vibrate className="w-5 h-5" />
          <span className="text-xs">Rung</span>
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={onShowHistory}
          disabled={disabled}
          className="flex flex-col items-center gap-1 h-16 p-2 border-gray-300 text-gray-600 hover:bg-gray-50"
        >
          <History className="w-5 h-5" />
          <span className="text-xs">Lịch sử</span>
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={onShowSettings}
          disabled={disabled}
          className="flex flex-col items-center gap-1 h-16 p-2 border-gray-300 text-gray-600 hover:bg-gray-50"
        >
          <Settings className="w-5 h-5" />
          <span className="text-xs">Cài đặt</span>
        </Button>
      </div>

      {/* Keyboard Shortcuts Info */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          Phím tắt: <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">R</kbd> Quét lại • 
          <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">M</kbd> Nhập tay • 
          <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Space</kbd> Check-in
        </p>
      </div>
    </div>
  )
}

export default ScannerControls
